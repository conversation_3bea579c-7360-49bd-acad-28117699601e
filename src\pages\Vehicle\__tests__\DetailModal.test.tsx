import { render, screen } from '@testing-library/react';
import dayjs from 'dayjs';
import React from 'react';
import DetailModal from '../DetailModal';

// Mock dayjs
jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  return {
    __esModule: true,
    default: jest.fn((date) => originalDayjs(date || '2025-07-02')),
  };
});

const mockVehicle: API.Vehicle = {
  id: 1,
  plateNumber: '京A12345',
  vehicleType: '小型车',
  status: '空闲',
  latitude: 39.9042,
  longitude: 116.4074,
  mileage: 15100.5,
  appearance: '车身整洁，右侧有轻微划痕',
  insuranceExpiry: '2024-12-31T00:00:00.000Z',
  licenseExpiry: '2025-06-30T00:00:00.000Z',
  supplies: '洗车工具、毛巾、清洁剂、新增吸尘器',
  lastSubmittedAt: '2025-07-02T14:30:00.000Z',
  lastSubmittedBy: 5,
  lastSubmittedEmployee: {
    id: 5,
    name: '张三',
    phone: '13800138000',
    walletBalance: 0,
    status: 1,
  },
  employee: {
    id: 5,
    name: '张三',
    phone: '13800138000',
    position: '洗护师',
    walletBalance: 0,
    status: 1,
  },
  createdAt: '2025-01-01T00:00:00.000Z',
  updatedAt: '2025-07-02T14:30:00.000Z',
};

describe('DetailModal', () => {
  beforeEach(() => {
    // Reset dayjs mock
    (dayjs as jest.MockedFunction<typeof dayjs>).mockImplementation(
      (date) => jest.requireActual('dayjs')(date || '2025-07-02')
    );
  });

  it('renders vehicle details correctly', () => {
    render(
      <DetailModal
        open={true}
        vehicle={mockVehicle}
        onClose={() => {}}
      />
    );

    // Check basic information
    expect(screen.getByText('京A12345')).toBeInTheDocument();
    expect(screen.getByText('小型车')).toBeInTheDocument();
    expect(screen.getByText('空闲')).toBeInTheDocument();

    // Check extended information
    expect(screen.getByText('15100.5 公里')).toBeInTheDocument();
    expect(screen.getByText('车身整洁，右侧有轻微划痕')).toBeInTheDocument();
    expect(screen.getByText('洗车工具、毛巾、清洁剂、新增吸尘器')).toBeInTheDocument();

    // Check employee information
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('13800138000')).toBeInTheDocument();
  });

  it('handles missing vehicle data gracefully', () => {
    render(
      <DetailModal
        open={true}
        vehicle={undefined}
        onClose={() => {}}
      />
    );

    // Should not render anything when vehicle is undefined
    expect(screen.queryByText('车辆详情')).not.toBeInTheDocument();
  });

  it('displays expiry status correctly', () => {
    const vehicleWithExpiredInsurance = {
      ...mockVehicle,
      insuranceExpiry: '2020-01-01T00:00:00.000Z', // Expired
      licenseExpiry: '2025-12-31T00:00:00.000Z', // Future
    };

    render(
      <DetailModal
        open={true}
        vehicle={vehicleWithExpiredInsurance}
        onClose={() => {}}
      />
    );

    // Should show expired status for insurance
    expect(screen.getByText(/已过期/)).toBeInTheDocument();
  });

  it('handles missing optional fields', () => {
    const minimalVehicle: API.Vehicle = {
      id: 1,
      plateNumber: '京B54321',
      status: '服务中',
    };

    render(
      <DetailModal
        open={true}
        vehicle={minimalVehicle}
        onClose={() => {}}
      />
    );

    expect(screen.getByText('京B54321')).toBeInTheDocument();
    expect(screen.getByText('服务中')).toBeInTheDocument();
    // Should show '-' for missing fields
    expect(screen.getAllByText('-')).toHaveLength(8); // Multiple missing fields
  });
});
